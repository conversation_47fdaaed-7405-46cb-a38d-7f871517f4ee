import React, { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const themes = {
  electric: {
    name: 'Electric Blue',
    primary: 'bg-blue-600',
    secondary: 'bg-blue-100',
    accent: 'bg-yellow-400',
    background: 'bg-gray-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-gray-200',
    card: 'bg-white',
    gradient: 'from-blue-500 to-blue-700'
  },
  dark: {
    name: 'Dark Mode',
    primary: 'bg-blue-600',
    secondary: 'bg-gray-700',
    accent: 'bg-blue-500',
    background: 'bg-gray-900',
    text: 'text-white',
    textSecondary: 'text-gray-300',
    border: 'border-gray-600',
    card: 'bg-gray-800',
    gradient: 'from-blue-600 to-blue-800'
  },
  green: {
    name: 'Eco Green',
    primary: 'bg-green-600',
    secondary: 'bg-green-100',
    accent: 'bg-lime-400',
    background: 'bg-green-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-green-200',
    card: 'bg-white',
    gradient: 'from-green-500 to-green-700'
  },

  teal: {
    name: 'Ocean Teal',
    primary: 'bg-teal-600',
    secondary: 'bg-teal-100',
    accent: 'bg-cyan-400',
    background: 'bg-teal-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-teal-200',
    card: 'bg-white',
    gradient: 'from-teal-500 to-blue-600'
  },

  pink: {
    name: 'Rose Pink',
    primary: 'bg-pink-600',
    secondary: 'bg-pink-100',
    accent: 'bg-rose-400',
    background: 'bg-pink-50',
    text: 'text-gray-900',
    textSecondary: 'text-gray-600',
    border: 'border-pink-200',
    card: 'bg-white',
    gradient: 'from-pink-500 to-rose-600'
  },

}



export function ThemeProvider({ children }) {
  const [currentTheme, setCurrentTheme] = useState('electric')

  // Load theme settings from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('prepaid-meter-theme')

    if (savedTheme && themes[savedTheme]) {
      setCurrentTheme(savedTheme)
    }
  }, [])

  // Save theme settings to localStorage
  useEffect(() => {
    localStorage.setItem('prepaid-meter-theme', currentTheme)
  }, [currentTheme])

  const value = {
    currentTheme,
    setCurrentTheme,
    theme: themes[currentTheme],
    themes
  }

  return (
    <ThemeContext.Provider value={value}>
      <div
        className={`${themes[currentTheme].background} ${themes[currentTheme].text} min-h-screen transition-all duration-300`}
        style={{ fontFamily: 'Inter, system-ui, -apple-system, sans-serif' }}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
